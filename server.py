from flask import Flask, request, jsonify, redirect, render_template_string, make_response
from werkzeug.middleware.proxy_fix import ProxyFix
from crypto_utils import CryptoUtils
import jwt, time
import requests
import json
import asyncio
import uuid
import os
from datetime import datetime, timezone, timedelta
import user_agents
import logging
import urllib.parse
from utils import Utils
VIETNAM_TZ = timezone(timedelta(hours=7))
logging.basicConfig(level=logging.DEBUG, format=
    '%(asctime)s %(levelname)s %(name)s: %(message)s', handlers=[logging.
    FileHandler('loggg.txt', encoding='utf-8'), logging.StreamHandler()])
logger = logging.getLogger('dashboard')
BASE_URL = 'https://dashboard.kingdev.sbs/'

# Cấu hình debug IP
IP_DEBUG_ENABLED = True  # Đặt False để tắt debug IP

# Rate limiting dictionary
from collections import defaultdict
request_counts = defaultdict(list)


def getRealIp(request):
    """
    Lấy IP thật của user từ các header khác nhau
    Ưu tiên theo thứ tự: CF-Connecting-IP > X-Real-IP > X-Forwarded-For > remote_addr
    """
    # Debug: Log tất cả IP headers
    debug_info = {
        'CF-Connecting-IP': request.headers.get('CF-Connecting-IP'),
        'X-Real-IP': request.headers.get('X-Real-IP'),
        'X-Forwarded-For': request.headers.get('X-Forwarded-For'),
        'HTTP_X_FORWARDED_FOR': request.environ.get('HTTP_X_FORWARDED_FOR'),
        'remote_addr': request.remote_addr
    }

    # Cloudflare IP (nếu dùng Cloudflare)
    cf_ip = request.headers.get('CF-Connecting-IP')
    if cf_ip:
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using CF-Connecting-IP: {cf_ip.strip()} | All headers: {debug_info}")
        return cf_ip.strip()

    # X-Real-IP (nginx proxy)
    real_ip = request.headers.get('X-Real-IP')
    if real_ip:
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using X-Real-IP: {real_ip.strip()} | All headers: {debug_info}")
        return real_ip.strip()

    # X-Forwarded-For (có thể có nhiều IP, lấy IP đầu tiên)
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        final_ip = forwarded_for.split(',')[0].strip()
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using X-Forwarded-For: {final_ip} | All headers: {debug_info}")
        return final_ip

    # HTTP_X_FORWARDED_FOR từ environ (fallback)
    environ_forwarded = request.environ.get('HTTP_X_FORWARDED_FOR')
    if environ_forwarded:
        final_ip = environ_forwarded.split(',')[0].strip()
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using HTTP_X_FORWARDED_FOR: {final_ip} | All headers: {debug_info}")
        return final_ip

    # Cuối cùng dùng remote_addr
    final_ip = request.remote_addr or 'Unknown'
    if IP_DEBUG_ENABLED:
        print(f"[IP_DEBUG] Using remote_addr: {final_ip} | All headers: {debug_info}")
    return final_ip


def getCaptchaHtml(token, taskType):
    return f"""
<!DOCTYPE html>
<html>
<head>
    <title>King - Bot Authorization</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <style>
        body {{
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: #000;
            color: #fff;
            font-family: 'JetBrains Mono', monospace;
        }}
        .logo {{
            width: 200px;
            margin-bottom: 20px;
        }}
        .text {{
            text-align: center;
            font-size: 24px;
            margin: 10px 0;
        }}
        .info {{
            font-size: 14px;
            color: #888;
            margin-top: 20px;
        }}
        .captcha-container {{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }}
        .submit-btn {{
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'JetBrains Mono', monospace;
            font-size: 16px;
            margin-top: 15px;
        }}
        .submit-btn:hover {{
            background: #45a049;
        }}
        .submit-btn:disabled {{
            background: #666;
            cursor: not-allowed;
        }}
        .error {{
            color: #ff4444;
            margin-top: 10px;
            text-align: center;
        }}
    </style>
</head>
<body>
    <img src="http://ltruowng.space/king.png" class="logo">
    <div class="text">Xác thực CAPTCHA</div>
    <div class="text">Vui lòng hoàn thành CAPTCHA để tiếp tục</div>

    <form action="/api/{taskType}/verify" method="POST" class="captcha-container">
        <input type="hidden" name="token" value="{token}">
        <div class="g-recaptcha" data-sitekey="6LcNvI0rAAAAAGSUGfp3_vC_G2pamEbNyE4oFKk2"></div>
        <button type="submit" class="submit-btn">Xác thực</button>
    </form>

    <div class="info">
        King Bot - Secure Authentication
    </div>
</body>
</html>
"""

def getSuccessHtml(currentUser, currentTime, userAvatar=None):
    avatarHtml = ''
    if userAvatar:
        avatarHtml = (
            f'<img src="{userAvatar}" style="width: 64px; height: 64px; border-radius: 50%; margin-bottom: 10px;">'
            )
    return f"""
<!DOCTYPE html>
<html>
<head>
    <title>King - Bot Authorization</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {{
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: #000;
            color: #fff;
            font-family: 'JetBrains Mono', monospace;
        }}
        .logo {{
            width: 200px;
            margin-bottom: 20px;
        }}
        .text {{
            text-align: center;
            font-size: 24px;
            margin: 10px 0;
        }}
        .info {{
            font-size: 14px;
            color: #888;
            margin-top: 20px;
        }}
        .userInfo {{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <img src="http://ltruowng.space/king.png" class="logo">
    <div class="text">Success - King Bot</div>
    <div class="text"> + 1, 25 POINT</div>
    <div class="userInfo">
        {avatarHtml}
        <div class="text">Authenticated by {currentUser}</div>
    </div>
    <div class="info">
        at {currentTime}
    </div>
</body>
</html>
"""

def verifyCaptcha(captchaResponse):
    if not captchaResponse:
        return False

    secret_key = "6LcNvI0rAAAAAMGANc21RhVKYPRYCbiMLQgC69iT"  # Replace with your actual secret key
    verify_url = "https://www.google.com/recaptcha/api/siteverify"

    data = {
        'secret': secret_key,
        'response': captchaResponse
    }

    try:
        response = requests.post(verify_url, data=data)
        result = response.json()
        return result.get('success', False)
    except Exception as e:
        print(f"CAPTCHA verification error: {e}")
        return False

def processTaskAuth(token, taskType, currentIp, apiKey=None, isRedirectEndpoint=None):
    user_agent = request.headers.get('User-Agent', '').lower()
    suspicious_agents = ['bot', 'crawler', 'spider', 'monitor', 'check', 'ping', 'health', 'uptime', 'curl', 'wget']
    if any(agent in user_agent for agent in suspicious_agents):
        print(f"[{taskType.upper()}] IGNORED - Monitoring/bot request from User-Agent: {user_agent}")
        return False, {'error': 'Monitoring request ignored'}, 400

    current_time = time.time()
    request_counts[currentIp] = [req_time for req_time in request_counts[currentIp] if current_time - req_time < 60]
    if len(request_counts[currentIp]) >= 10:
        print(f"[{taskType.upper()}] RATE LIMITED - Too many requests from IP: {currentIp}")
        return False, {'error': 'Rate limit exceeded'}, 429
    request_counts[currentIp].append(current_time)

    logger.debug(
        f'[{taskType.upper()}][REQ] token={token[:8]}, currentIp={currentIp}')
    print(
        f'[{taskType.upper()}] Processing token: {token[:20]}... from IP: {currentIp}'
        )
    try:
        payload = jwt.decode(token, CryptoUtils.jwtSecret, algorithms=['HS256']
            )
        decryptedData = CryptoUtils.decrypt(payload['data'])

        print(f"[DEBUG] decryptedData type: {type(decryptedData)}, value: {decryptedData}")

        # Check if decryptedData is None or empty
        if not decryptedData:
            print(f"[{taskType.upper()}] ERROR - decryptedData is None or empty")
            return False, {'error': 'Invalid token data - King Bot'}, 400

        # Skip early token validation for LinkNgon/TrafficUser redirect endpoints
        # They have their own specific validation logic later
        if not (taskType in ['linkngon', 'trafficuser'] and isRedirectEndpoint is True):
            if Utils.isTokenUsed(token):
                print(
                    f'[{taskType.upper()}] Token already used (pre-check): {token[:20]}...'
                    )
                return False, {'error': 'TOKEN ALREADY USED - King Bot'}, 400

        if Utils.isTaskLinkUsedByToken(token):
            print(
                f'[{taskType.upper()}] Task link already used: {token[:20]}...'
                )
            return False, {'error': 'LINK ALREADY USED - King Bot'}, 400

        # Try to parse decryptedData as JSON
        try:
            dataObj = json.loads(decryptedData)
            print(f"[DEBUG] Parsed dataObj: {type(dataObj)}, value: {dataObj}")
        except Exception as e:
            print(f"[DEBUG] JSON parse error: {e}, decryptedData: {decryptedData}")
            # If it's not JSON, treat it as a simple user ID string
            dataObj = None
        if dataObj and isinstance(dataObj, dict) and 'auth' in dataObj:
            if dataObj['auth'] == 'valid':
                authIp = dataObj.get('ip', '')
                currentIpFirst = currentIp
                logger.debug(
                    f"[{taskType.upper()}][VERIFY] userId={dataObj.get('uid')}, token={token[:8]}, authIp={authIp}, currentIp={currentIpFirst}"
                    )

                # Get redirectToken first before using it
                redirectToken = dataObj.get('redirectToken')

                # Get time variables first
                currentTime = int(time.time())
                authTime = dataObj.get('time', 0)

                # LOGIC MỚI: Sử dụng isRedirectEndpoint để phân biệt rõ ràng
                if taskType in ['linkngon', 'trafficuser']:
                    # Kiểm tra xem user đã hoàn thành task này hôm nay chưa
                    if taskType == 'linkngon':
                        isTaskCompleted = Utils.isLinkngonUsedToday(dataObj.get('uid'))
                    else:  # trafficuser
                        isTaskCompleted = Utils.isTrafficuserUsedToday(dataObj.get('uid'))

                    if isRedirectEndpoint is True:
                        # Đây là /redirect endpoint - lần đầu user click (tạo shortened URL)
                        isAuthUrlRequest = False
                        print(f"[{taskType.upper()}] REDIRECT ENDPOINT - Creating shortened URL")
                        print(f"[{taskType.upper()}] - isTaskCompleted: {isTaskCompleted}")
                        print(f"[{taskType.upper()}] - redirectToken: {redirectToken[:20] if redirectToken else 'None'}")
                    elif isRedirectEndpoint is False:
                        # Đây là /auth endpoint - user hoàn thành task từ shortened URL
                        print(f"[{taskType.upper()}] AUTH ENDPOINT - Checking conditions:")
                        print(f"[{taskType.upper()}] - isTaskCompleted: {isTaskCompleted}")
                        print(f"[{taskType.upper()}] - redirectToken: {redirectToken[:20] if redirectToken else 'None'}")
                        print(f"[{taskType.upper()}] - authTime: {authTime}, currentTime: {currentTime}")

                        # LOGIC ĐÚNG: Chỉ cho phép auth khi có redirectToken từ shortened URL
                        if not isTaskCompleted and redirectToken is not None:
                            # Kiểm tra xem token hiện tại có khác với redirectToken không
                            currentToken = request.args.get('token')

                            print(f"[{taskType.upper()}] TOKEN COMPARISON:")
                            print(f"  - Current token: {currentToken[:30]}...")
                            print(f"  - Redirect token: {redirectToken[:30]}...")
                            print(f"  - Tokens are different: {currentToken != redirectToken}")

                            # CHỈ cho phép auth khi:
                            # 1. Token hiện tại KHÁC với redirectToken (nghĩa là đây là JWT mới từ shortened URL)
                            # 2. Có delay hợp lý
                            if currentToken != redirectToken:
                                timeDelay = currentTime - authTime
                                minDelayRequired = 30  # Tăng delay lên 30 giây
                                isAuthUrlRequest = timeDelay >= minDelayRequired
                                print(f"[{taskType.upper()}] VALID AUTH REQUEST - Time delay: {timeDelay}s (min: {minDelayRequired}s)")
                                print(f"[{taskType.upper()}] - isAuthUrlRequest: {isAuthUrlRequest}")
                            else:
                                # Token giống nhau = đây là auto-request từ service, KHÔNG phải user
                                isAuthUrlRequest = False
                                print(f"[{taskType.upper()}] REJECTED - Same token = Auto-request from service, not user")
                                print(f"[{taskType.upper()}] - Current token: {currentToken[:20]}...")
                                print(f"[{taskType.upper()}] - Redirect token: {redirectToken[:20]}...")
                                print(f"[{taskType.upper()}] - User-Agent: {request.headers.get('User-Agent', 'Unknown')}")
                        else:
                            isAuthUrlRequest = False
                            print(f"[{taskType.upper()}] AUTH ENDPOINT - Task already completed or no redirectToken")
                    else:
                        # KHÔNG CHO PHÉP fallback cho LinkNgon/TrafficUser - phải sử dụng endpoint mới
                        isAuthUrlRequest = False
                        print(f"[{taskType.upper()}] ERROR - isRedirectEndpoint is None! Must use /redirect or /auth endpoint.")
                        print(f"[{taskType.upper()}] Current request path: {request.path}")
                        return False, {'error': 'Invalid endpoint. Use /redirect or /auth endpoint.'}, 400
                else:
                    # Cho yeumoney, giữ logic cũ
                    isAuthUrlRequest = redirectToken is not None

                # Debug log đã được tích hợp vào logic trên
                print(f"[{taskType.upper()}] Final isAuthUrlRequest: {isAuthUrlRequest}")


                # Chỉ đánh dấu redirectToken used khi thực sự hoàn thành task
                if redirectToken and isAuthUrlRequest:
                    Utils.markTokenAsUsed(redirectToken)
                    print(
                        f'[{taskType.upper()}] Marked redirect token as used: {redirectToken[:20]}...'
                        )

                # Logic xử lý token cho LinkNgon và TrafficUser
                if taskType in ['linkngon', 'trafficuser']:
                    if isAuthUrlRequest:
                        # Khi user thực sự hoàn thành task (đến authUrl), đánh dấu token used
                        if not Utils.tryUseToken(token):
                            print(
                                f'[{taskType.upper()}] Token already used: {token[:20]}...'
                                )
                            return False, {'error': 'TOKEN ALREADY USED - King Bot'
                                }, 400
                    else:
                        # Khi user chỉ mới click link lần đầu, chỉ kiểm tra không đánh dấu used
                        if Utils.isTokenUsed(token):
                            print(
                                f'[{taskType.upper()}] Token already used: {token[:20]}...'
                                )
                            return False, {'error': 'TOKEN ALREADY USED - King Bot'
                                }, 400
                        # Không gọi tryUseToken ở đây để tránh đánh dấu token used sớm
                else:
                    # Cho yeumoney, giữ logic cũ
                    if not Utils.tryUseToken(token):
                        print(
                            f'[{taskType.upper()}] Token already used: {token[:20]}...'
                            )
                        return False, {'error': 'TOKEN ALREADY USED - King Bot'
                            }, 400
                print(
                    f"[{taskType.upper()}] Processing auth data for user: {dataObj.get('uid', 'unknown')}"
                    )
                timeDiff = currentTime - authTime
                userId = dataObj.get('uid')
                ip = dataObj.get('ip', 'Unknown')
                userAgent = dataObj.get('ua', 'Unknown')
                commandUserId = dataObj.get('commandUserId')
                if commandUserId and commandUserId != userId:
                    return False, {'error': '? - King Bot'}, 400
                if not Utils.checkIpSecurity(userId, currentIp):
                    return False, {'error': 'IP Security Violation'}, 403
                suspicious = False
                if taskType == 'yeumoney':
                    suspicious = timeDiff < 1
                    if suspicious:
                        Utils.rollbackTokenUsage(token)
                        Utils.logUserActivity(userId, taskType, ip,
                            userAgent, timeDiff, suspicious)
                        print(
                            f'[{taskType.upper()}] Bypass detected, rolling back token: {token[:20]}...'
                            )
                        return False, {'error': 'WTF !! Stupid bitch'}, 400
                Utils.logUserActivity(userId, taskType, ip, userAgent,
                    timeDiff, suspicious)
                if Utils.isUserBanned(userId):
                    return False, {'error': 'User is banned'}, 403
                if taskType == 'yeumoney':
                    maxDaily = 3
                    if Utils.isYeumoneyLocked(userId, maxDaily):
                        return False, {'error':
                            f'LOCKED UNTIL TOMORROW - {maxDaily} times per day'
                            }, 403
                    progressData = Utils.getYeumoneyProgressData(userId)
                    currentProgress = progressData['PROGRESS_LEVEL'
                        ] if progressData else 0
                    newProgress = currentProgress + 1
                    Utils.addPoints(userId, 1.25)
                    Utils.incrementYeumoneyProgress(userId)
                    Utils.saveYeumoneyProgress(userId, newProgress, '')
                    Utils.markTaskLinkAsUsedByToken(token)
                    print(
                        f'[{taskType.upper()}] Successfully completed, progress updated for user: {userId} ({newProgress}/{maxDaily})'
                        )
                elif taskType == 'linkngon':
                    print(f'[{taskType.upper()}] Processing completion logic, isAuthUrlRequest: {isAuthUrlRequest}')
                    if isAuthUrlRequest:
                        # User thực sự hoàn thành task (đến authUrl từ shortened URL với đủ delay)
                        print(f'[{taskType.upper()}] Adding points and marking as used...')
                        Utils.addPoints(userId, 1.25)
                        Utils.markLinkngonUsed(userId)
                        Utils.markTaskLinkAsUsedByToken(token)
                        # Lấy timeDelay từ scope trước đó
                        delay_info = ""
                        if isRedirectEndpoint is False and 'timeDelay' in locals():
                            delay_info = f" (delay: {timeDelay}s)"
                        print(f'[{taskType.upper()}] TASK COMPLETED! User accessed authUrl from shortened URL{delay_info}.')
                    else:
                        print(f'[{taskType.upper()}] Task NOT completed - isAuthUrlRequest is False')
                        if isRedirectEndpoint is True:
                            print(f'[{taskType.upper()}] Redirect endpoint - creating shortened URL.')
                        elif isRedirectEndpoint is False:
                            print(f'[{taskType.upper()}] Auth endpoint - but conditions not met for completion.')
                        else:
                            print(f'[{taskType.upper()}] Unknown endpoint type.')
                elif taskType == 'trafficuser':
                    print(f'[{taskType.upper()}] Processing completion logic, isAuthUrlRequest: {isAuthUrlRequest}')
                    if isAuthUrlRequest:
                        # User thực sự hoàn thành task (đến authUrl từ shortened URL với đủ delay)
                        print(f'[{taskType.upper()}] Adding points and marking as used...')
                        Utils.addPoints(userId, 1.25)
                        Utils.markTrafficuserUsed(userId)
                        Utils.markTaskLinkAsUsedByToken(token)
                        # Lấy timeDelay từ scope trước đó
                        delay_info = ""
                        if isRedirectEndpoint is False and 'timeDelay' in locals():
                            delay_info = f" (delay: {timeDelay}s)"
                        print(f'[{taskType.upper()}] TASK COMPLETED! User accessed authUrl from shortened URL{delay_info}.')
                    else:
                        print(f'[{taskType.upper()}] Task NOT completed - isAuthUrlRequest is False')
                        if isRedirectEndpoint is True:
                            print(f'[{taskType.upper()}] Redirect endpoint - creating shortened URL.')
                        elif isRedirectEndpoint is False:
                            print(f'[{taskType.upper()}] Auth endpoint - but conditions not met for completion.')
                        else:
                            print(f'[{taskType.upper()}] Unknown endpoint type.')

                # Only return success HTML if the task was actually completed
                if isAuthUrlRequest:
                    userInfo = getUserInfo(userId)
                    currentUser = userInfo.get('displayName', 'Unknown')
                    userAvatar = userInfo.get('avatar', '')
                    currentTime = Utils.getFormattedDateTime() if hasattr(Utils,
                        'getFormattedDateTime') else datetime.now().strftime(
                        '%d/%m/%Y %H:%M')
                    successHtml = getSuccessHtml(currentUser, currentTime,
                        userAvatar)
                    return True, successHtml, 200

                # For redirect endpoints or incomplete tasks, continue to URL creation logic
                # Don't return here - let the code continue to URL creation
                print(f'[{taskType.upper()}] Continuing to URL creation logic for redirect endpoint')
            else:
                return False, {'auth': 'unvalid'}, 400

        # Check if this is a verify endpoint call (from CAPTCHA form)
        # If dataObj is None/invalid and this is not a redirect endpoint, return error
        if not dataObj or not isinstance(dataObj, dict):
            if isRedirectEndpoint is False:
                print(f'[{taskType.upper()}] ERROR - Invalid token data for verify endpoint')
                return False, {'error': 'Invalid token data - King Bot'}, 400

        print(f'[{taskType.upper()}] Creating auth data for original token')
        ip = currentIp
        logger.debug(
            f'[{taskType.upper()}][CREATE] token={token[:8]}, ip_saved={ip}')
        userAgent = request.headers.get('User-Agent')
        currentTime = int(time.time())
        authData = {'auth': 'valid', 'uid': decryptedData, 'ip': ip, 'ua':
            userAgent, 'time': currentTime, 'redirectToken': token,
            'commandUserId': decryptedData}
        encryptedAuthData = CryptoUtils.encrypt(json.dumps(authData))
        jwtToken = CryptoUtils.createJwt(encryptedAuthData)
        # Tạo authUrl với endpoint phù hợp
        if taskType in ['linkngon', 'trafficuser']:
            authUrl = f'{BASE_URL}api/{taskType}/auth?token={jwtToken}'
        else:
            authUrl = f'{BASE_URL}api/{taskType}?token={jwtToken}'
        savedUrl = Utils.getTokenUrl(token)
        if savedUrl:
            print(
                f'[{taskType.upper()}] Using saved URL for token: {token[:20]}...'
                )
            return True, savedUrl, 302
        if taskType == 'yeumoney':
            # ✅ Debug dataObj type
            print(f"[DEBUG] dataObj type: {type(dataObj)}, value: {dataObj}")

            # ✅ Lấy userId từ dataObj để check mode
            # Fix: Check if dataObj is a dictionary before calling .get()
            if isinstance(dataObj, dict):
                userId = dataObj.get('uid')
            else:
                # If dataObj is not a dict, use decryptedData as userId
                userId = decryptedData
            print(f"[DEBUG] userId: {userId}")

            apiKey = (
                'a6f62ee08685c27576f15c65504d4126c12d384f7ecd8fc69f49aea44fdbd6a6'
                )
            apiUrl = 'https://yeumoney.com/QL_api.php'
            params = {'token': apiKey, 'format': 'json', 'url': authUrl}
            print(
                f'[{taskType.upper()}] Creating shortened URL with params: {params}'
                )
            r = requests.get(apiUrl, params=params)
            try:
                result = r.json()
                if result.get('status') == 'success':
                    shortenedUrl = result.get('shortenedUrl')
                    if shortenedUrl:
                        Utils.saveTokenUrl(token, shortenedUrl)
                        print(
                            f'[{taskType.upper()}] Saved shortened URL for token: {token[:20]}...'
                            )
                        return True, shortenedUrl, 302
            except Exception as e:
                print(f'Error creating shortened URL: {e}')
            return True, 'https://yeumoney.com/', 302
        elif taskType == 'linkngon':
            try:
                linkngonApi = (
                    f'https://kingdev.sbs/linkngonapi/shorten?url={authUrl}')
                headers = {
                    'Cookie': 'cf_clearance=jY89pqZ.K_WeyeQH1PFmKEGuZr8amor9oxWoz2rKLns-1753324142-*******-pCqQIlqjMWjXHQIVokf_t0g.qIbOnXJ2OIpMOnwd0vasDRVuPie.G6bIf8DkU9c8gP5F63Zp6b6MOVQbjrCL5Lo.yXMezXOOICMencPusxs2SO2ETdRY6sfiL75I8Hh6dTHcLnZISdG1w2nkbFzphLhzjYnUOMcaRdoq0ha1RzTppEOV5awf9xBnEUmBanR_xKpURI3wQtOEVL3LMt3lUtwsVoB6.m8ee5Ct2LNWijA'
                }
                response = requests.get(linkngonApi, headers=headers)
                result = response.json()
                if result.get('shortened'):
                    shortenedUrl = result['shortened']
                    Utils.saveTokenUrl(token, shortenedUrl)
                    print(
                        f'[{taskType.upper()}] Saved shortened URL for token: {token[:20]}...'
                        )
                    return True, shortenedUrl, 302
                else:
                    return False, {'error': 'Failed to create LinkNgon URL'
                        }, 500
            except Exception as e:
                print(f'Error creating LinkNgon URL: {e}')
                return False, {'error': f'LinkNgon API error: {str(e)}'}, 500
        elif taskType == 'trafficuser':
            try:
                trafficuserApi = 'https://kingdev.sbs/trafficuser/shorten'
                params = {'url': authUrl}
                headers = {
                    'Cookie': "cf_clearance=jY89pqZ.K_WeyeQH1PFmKEGuZr8amor9oxWoz2rKLns-1753324142-*******-pCqQIlqjMWjXHQIVokf_t0g.qIbOnXJ2OIpMOnwd0vasDRVuPie.G6bIf8DkU9c8gP5F63Zp6b6MOVQbjrCL5Lo.yXMezXOOICMencPusxs2SO2ETdRY6sfiL75I8Hh6dTHcLnZISdG1w2nkbFzphLhzjYnUOMcaRdoq0ha1RzTppEOV5awf9xBnEUmBanR_xKpURI3wQtOEVL3LMt3lUtwsVoB6.m8ee5Ct2LNWijA"
                }
                response = requests.get(trafficuserApi, params=params, headers=headers)
                result = response.json()
                if result.get('shortened'):
                    shortenedUrl = result['shortened']
                    Utils.saveTokenUrl(token, shortenedUrl)
                    print(
                        f'[{taskType.upper()}] Saved shortened URL for token: {token[:20]}...'
                        )
                    return True, shortenedUrl, 302
                else:
                    return False, {'error': 'Failed to create TrafficUser URL'
                        }, 500
            except Exception as e:
                print(f'Error creating TrafficUser URL: {e}')
                return False, {'error': f'TrafficUser API error: {str(e)}'
                    }, 500
        return False, {'error': 'Unknown task type'}, 400
    except jwt.InvalidSignatureError:
        return False, {'error': 'INVALID JWT SIGNATURE - King Bot'}, 401
    except jwt.DecodeError:
        return False, {'error': 'MALFORMED JWT - King Bot'}, 400
    except Exception as e:
        print(f'{taskType} API error: {e}')
        return False, {'error': 'UNKNOWN ERROR - King Bot', 'detail': str(e)
            }, 500


def getUserInfo(userId, userJson=None):
    if userJson:
        Utils.updateUserInfo(userId, userJson)
    user = Utils.getOrCreateUser(userId, userJson)
    return {'id': userId, 'username': user['USER_NAME'] or
        f'User{userId[:8]}', 'displayName': user['DISPLAY_NAME'] or
        f'User{userId[:8]}', 'avatar': user['AVATAR_URL']}


class FlashServer:

    def __init__(self, discordBot=None):
        self.app = Flask(__name__)
        self.app.wsgi_app = ProxyFix(self.app.wsgi_app, x_for=1, x_proto=1,
            x_host=1, x_prefix=1)
        self.discordBot = None
        self.verifyKey = 'KingBot@@@`'
        self.setupRoutes()
        logger.info('FlashServer initialized with ProxyFix.')

    def setupRoutes(self):

        @self.app.route('/')
        def dashboard():
            return self.getDashboardHtml()

        @self.app.route('/ban', methods=['POST'])
        def banUserRoute():
            data = request.get_json()
            userId = data.get('userId')
            duration = data.get('duration', 1)
            userJson = data.get('user')
            if userId:
                if userJson:
                    Utils.updateUserInfo(userId, userJson)
                banReason = 'Manual ban from dashboard'
                if duration == -1:
                    banReason = 'King la li do !'
                actualDuration = Utils.banUserCustom(userId, banReason,
                    duration)
                userInfo = getUserInfo(userId, userJson)
                return jsonify({'success': True, 'duration': actualDuration,
                    'user': userInfo})
            return jsonify({'error': 'Invalid user ID'}), 400

        @self.app.route('/unban', methods=['POST'])
        def unbanUserRoute():
            data = request.get_json()
            userId = data.get('userId')
            userJson = data.get('user')
            if userId:
                if userJson:
                    Utils.updateUserInfo(userId, userJson)
                success = Utils.unbanUser(userId)
                userInfo = getUserInfo(userId, userJson)
                return jsonify({'success': success, 'user': userInfo})
            return jsonify({'error': 'Invalid user ID'}), 400

        @self.app.route('/user-details', methods=['GET', 'POST'])
        def getUserDetails():
            if request.method == 'POST':
                reqData = request.get_json()
                userId = reqData.get('userId')
                userJson = reqData.get('user')
            else:
                userId = request.args.get('userId')
                userJson = None
            if not userId:
                return jsonify({'error': 'Missing user ID'}), 400
            if userJson:
                Utils.updateUserInfo(userId, userJson)
            userInfo = getUserInfo(userId, userJson)
            user = Utils.getOrCreateUser(userId)
            logs = Utils.getUserLogs(userId, 50)
            exchangeHistory = Utils.getUserExchangeHistory(userId, 20)
            banHistory = Utils.getUserBanHistory(userId)
            formattedLogs = []
            for log in logs:
                formattedLogs.append({'timestamp': log['CREATED_AT'].
                    strftime('%d/%m/%Y %H:%M'), 'site': log['SITE_NAME'],
                    'ip': log['IP_ADDRESS'], 'browser': log['BROWSER_INFO'],
                    'timeDiff': log['TIME_DIFF'], 'bypassDetected': log[
                    'BYPASS_DETECTED']})
            formattedExchanges = []
            for exchange in exchangeHistory:
                formattedExchanges.append({'timestamp': exchange[
                    'CREATED_AT'].strftime('%d/%m/%Y %H:%M'),
                    'pointsExchanged': exchange['POINTS_EXCHANGED'],
                    'accountsReceived': exchange['ACCOUNTS_RECEIVED'],
                    'exchangeType': exchange['EXCHANGE_TYPE'],
                    'accountText': exchange['ACCOUNT_TEXT']})
            formattedBans = []
            for ban in banHistory:
                formattedBans.append({'banTime': ban['CREATED_AT'].strftime
                    ('%d/%m/%Y %H:%M'), 'reason': ban['BAN_REASON'],
                    'duration': ban['BAN_DURATION'], 'banEnd': ban[
                    'BAN_END'].isoformat() if ban['BAN_END'] else None,
                    'active': ban['IS_ACTIVE']})
            return jsonify({'user': userInfo, 'points': {'yeumoney': user[
                'YEUMONEY_POINTS'], 'total': user['TOTAL_POINTS']}, 'logs':
                formattedLogs, 'exchangeHistory': formattedExchanges,
                'banHistory': formattedBans})

        @self.app.route('/delete-ban-history', methods=['POST'])
        def deleteBanHistory():
            data = request.get_json()
            userId = data.get('userId')
            userJson = data.get('user')
            if userId:
                if userJson:
                    Utils.updateUserInfo(userId, userJson)
                success = Utils.deleteBanHistory(userId)
                userInfo = getUserInfo(userId, userJson)
                return jsonify({'success': success, 'user': userInfo})
            return jsonify({'error': 'Invalid user ID'}), 400

        @self.app.route('/api/yeumoney', methods=['GET'])
        def yeumoneyApi():
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'yeumoney', currentIp)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/api/linkngon/redirect', methods=['GET'])
        def linkngonRedirectApi():
            """Endpoint cho lần đầu user click link - tạo shortened URL"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'linkngon', currentIp, isRedirectEndpoint=True)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/api/linkngon/auth', methods=['GET'])
        def linkngonAuthApi():
            """Endpoint cho khi user hoàn thành task từ shortened URL - hiển thị CAPTCHA"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400

            # Validate token first
            try:
                payload = jwt.decode(token, CryptoUtils.jwtSecret, algorithms=['HS256'])
                decryptedData = CryptoUtils.decrypt(payload['data'])
                # Token is valid, show CAPTCHA form with 401 status (Unauthorized - needs CAPTCHA)
                captchaHtml = getCaptchaHtml(token, 'linkngon')
                return captchaHtml, 401, {'Content-Type': 'text/html; charset=utf-8'}
            except Exception as e:
                print(f'[LINKNGON] Invalid token: {e}')
                return jsonify({'error': '? - King Bot'}), 400

        @self.app.route('/api/linkngon/verify', methods=['POST'])
        def linkngonVerifyApi():
            """Endpoint xử lý CAPTCHA và hoàn thành task LinkNgon"""
            token = request.form.get('token')
            captcha_response = request.form.get('g-recaptcha-response')

            if not token:
                return jsonify({'error': '? - King Bot'}), 400

            if not captcha_response:
                return "CAPTCHA không được hoàn thành. Vui lòng thử lại.", 400

            # Verify CAPTCHA
            if not verifyCaptcha(captcha_response):
                return "CAPTCHA không hợp lệ. Vui lòng thử lại.", 400

            # Process task after successful CAPTCHA
            currentIp = getRealIp(request)
            try:
                success, response_data, status_code = processTaskAuth(token,
                    'linkngon', currentIp, isRedirectEndpoint=False)
                if success:
                    if status_code == 302:
                        return redirect(response_data, code=302)
                    else:
                        return response_data, status_code, {'Content-Type':
                            'text/html; charset=utf-8'}
                else:
                    print(f"[LINKNGON] Verify failed: {response_data}")
                    return jsonify(response_data), status_code
            except Exception as e:
                print(f"[LINKNGON] Verify error: {e}")
                return jsonify({'error': 'UNKNOWN ERROR - King Bot', 'detail': str(e)}), 500

        @self.app.route('/api/trafficuser/redirect', methods=['GET'])
        def trafficuserRedirectApi():
            """Endpoint cho lần đầu user click link - tạo shortened URL"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'trafficuser', currentIp, isRedirectEndpoint=True)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/api/trafficuser/auth', methods=['GET'])
        def trafficuserAuthApi():
            """Endpoint cho khi user hoàn thành task từ shortened URL - hiển thị CAPTCHA"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400

            # Validate token first
            try:
                payload = jwt.decode(token, CryptoUtils.jwtSecret, algorithms=['HS256'])
                decryptedData = CryptoUtils.decrypt(payload['data'])
                # Token is valid, show CAPTCHA form with 401 status (Unauthorized - needs CAPTCHA)
                captchaHtml = getCaptchaHtml(token, 'trafficuser')
                return captchaHtml, 401, {'Content-Type': 'text/html; charset=utf-8'}
            except Exception as e:
                print(f'[TRAFFICUSER] Invalid token: {e}')
                return jsonify({'error': '? - King Bot'}), 400

        @self.app.route('/api/trafficuser/verify', methods=['POST'])
        def trafficuserVerifyApi():
            """Endpoint xử lý CAPTCHA và hoàn thành task TrafficUser"""
            token = request.form.get('token')
            captcha_response = request.form.get('g-recaptcha-response')

            if not token:
                return jsonify({'error': '? - King Bot'}), 400

            if not captcha_response:
                return "CAPTCHA không được hoàn thành. Vui lòng thử lại.", 400

            # Verify CAPTCHA
            if not verifyCaptcha(captcha_response):
                return "CAPTCHA không hợp lệ. Vui lòng thử lại.", 400

            # Process task after successful CAPTCHA
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'trafficuser', currentIp, isRedirectEndpoint=False)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/verify', methods=['GET', 'POST'])
        def verify():
            error = None
            if request.method == 'POST':
                key = request.form.get('key', '')
                logger.debug(f'Verify POST with key: {key}')
                if key == self.verifyKey:
                    logger.info(f'Dashboard access granted for key: {key}')
                    resp = make_response(redirect('/'))
                    resp.set_cookie('Author', self.verifyKey, max_age=60 * 
                        60 * 24 * 7)
                    return resp
                else:
                    logger.warning(f'Dashboard access denied for key: {key}')
                    error = 'Sai key! Vui lòng thử lại.'
            return render_template_string(
                """
                <!DOCTYPE html>
                <html lang="vi">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Dashboard Verification</title>
                    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700&family=Montserrat:wght@600;900&display=swap" rel="stylesheet">
                    <style>
                        body {
                            min-height: 100vh;
                            margin: 0;
                            padding: 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: linear-gradient(135deg, #232526 0%, #414345 100%);
                            font-family: 'Montserrat', 'JetBrains Mono', monospace;
                        }
                        .verifyContainer {
                            background: rgba(30, 32, 40, 0.95);
                            border-radius: 24px;
                            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
                            padding: 48px 36px 36px 36px;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            min-width: 340px;
                            max-width: 90vw;
                        }
                        .verifyLogo {
                            width: 72px;
                            height: 72px;
                            border-radius: 16px;
                            margin-bottom: 18px;
                            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
                        }
                        .verifyTitle {
                            font-size: 2.2rem;
                            font-weight: 900;
                            color: #fff;
                            margin-bottom: 8px;
                            letter-spacing: 1px;
                            text-shadow: 0 2px 8px #0004;
                        }
                        .verifyDesc {
                            color: #b0b3b8;
                            font-size: 1.05rem;
                            margin-bottom: 28px;
                            text-align: center;
                        }
                        .verifyForm {
                            width: 100%;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                        }
                        .verifyInput {
                            width: 100%;
                            padding: 14px 18px;
                            font-size: 1.1rem;
                            border-radius: 12px;
                            border: none;
                            outline: none;
                            background: #23272f;
                            color: #fff;
                            margin-bottom: 18px;
                            box-shadow: 0 2px 8px #0002;
                            transition: background 0.2s;
                        }
                        .verifyInput:focus {
                            background: #2d313a;
                        }
                        .verifyBtn {
                            width: 100%;
                            padding: 14px 0;
                            font-size: 1.1rem;
                            font-weight: 700;
                            border-radius: 12px;
                            border: none;
                            background: linear-gradient(90deg, #00c6ff 0%, #0072ff 100%);
                            color: #fff;
                            cursor: pointer;
                            box-shadow: 0 2px 8px #0072ff44;
                            transition: background 0.2s, transform 0.1s;
                        }
                        .verifyBtn:hover {
                            background: linear-gradient(90deg, #0072ff 0%, #00c6ff 100%);
                            transform: translateY(-2px) scale(1.03);
                        }
                        .verifyError {
                            margin-top: 18px;
                            color: #ff4d4f;
                            background: #2d1a1a;
                            border-radius: 8px;
                            padding: 10px 18px;
                            font-size: 1.05rem;
                            font-weight: 600;
                            box-shadow: 0 2px 8px #ff4d4f22;
                            letter-spacing: 0.5px;
                        }
                        @media (max-width: 500px) {
                            .verifyContainer {
                                padding: 28px 8vw 24px 8vw;
                                min-width: unset;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="verifyContainer">
                        <img src="http://ltruowng.space/king.png" class="verifyLogo" alt="Logo" />
                        <div class="verifyTitle">Dashboard Access</div>
                        <div class="verifyDesc">Nhập <b>key truy cập</b> để xác thực quyền vào dashboard.<br>Liên hệ admin nếu bạn chưa có key.</div>
                        <form class="verifyForm" method="post">
                            <input class="verifyInput" type="password" name="key" placeholder="Nhập key truy cập..." required autocomplete="off" />
                            <button class="verifyBtn" type="submit">Xác thực</button>
                        </form>
                        {% if error %}<div class="verifyError">{{ error }}</div>{% endif %}
                    </div>
                </body>
                </html>
            """
                , error=error)

        @self.app.before_request
        def requireVerify():
            if request.endpoint in ('verify', 'static'):
                return None
            if request.path.startswith('/api/') or request.path.startswith(
                '/user-details'):
                return None
            authorCookie = request.cookies.get('Author')
            if authorCookie != self.verifyKey:
                logger.debug(
                    f'Require verify: missing/invalid Author cookie: {authorCookie}'
                    )
                return redirect('/verify')
            logger.debug(f'Require verify: valid Author cookie: {authorCookie}'
                )
            return None

    def getDashboardHtml(self):
        logger.info('Dashboard accessed.')
        allUsers = Utils.getAllUsers()
        users = []
        for user in allUsers:
            try:
                userId = user['USER_ID']
                logs = Utils.getUserLogs(userId, 10)
                formattedLogs = []
                for log in logs:
                    formattedLogs.append({'timestamp': log['CREATED_AT'].
                        strftime('%d/%m/%Y %H:%M'), 'site': log['SITE_NAME'
                        ], 'ip': log['IP_ADDRESS'], 'browser': log[
                        'BROWSER_INFO'], 'timeDiff': log['TIME_DIFF'],
                        'bypassDetected': log['BYPASS_DETECTED']})
                bypassDetected = any(log['BYPASS_DETECTED'] for log in logs)
                banned = Utils.isUserBanned(userId)
                exchangeHistory = Utils.getUserExchangeHistory(userId, 5)
                formattedExchanges = []
                for exchange in exchangeHistory:
                    formattedExchanges.append({'timestamp': exchange[
                        'CREATED_AT'].strftime('%d/%m/%Y %H:%M'),
                        'pointsExchanged': exchange['POINTS_EXCHANGED'],
                        'accountsReceived': exchange['ACCOUNTS_RECEIVED'],
                        'exchangeType': exchange['EXCHANGE_TYPE'],
                        'accountText': exchange['ACCOUNT_TEXT']})
                totalExchanges = len(exchangeHistory)
                totalAccountsReceived = sum(exchange['ACCOUNTS_RECEIVED'] for
                    exchange in exchangeHistory)
                userData = {'id': userId, 'username': user['USER_NAME'] or
                    f'User{userId[:8]}', 'displayName': user['DISPLAY_NAME'
                    ] or f'User{userId[:8]}', 'avatar': user['AVATAR_URL'],
                    'yeumoneyPoints': user['YEUMONEY_POINTS'],
                    'site2sPoints': 0, 'totalPoints': user['TOTAL_POINTS'],
                    'logs': formattedLogs, 'suspicious': bypassDetected,
                    'banned': banned, 'exchangeHistory': formattedExchanges,
                    'totalExchanges': totalExchanges,
                    'totalAccountsReceived': totalAccountsReceived,
                    'totalTasks': 0, 'totalTaskAccounts': 0}
                users.append(userData)
            except Exception as e:
                print(f"Error processing user {user['USER_ID']}: {e}")
        users.sort(key=lambda x: (not x['suspicious'], -x['totalPoints']))
        bannedUserIds = Utils.getBannedUsers()
        bannedUsers = []
        for userId in bannedUserIds:
            try:
                user = Utils.getOrCreateUser(userId)
                if not user:
                    continue
                banHistory = Utils.getUserBanHistory(userId)
                if banHistory:
                    lastBan = banHistory[0]
                    duration = lastBan['BAN_DURATION']
                    if duration == -1:
                        durationText = 'Vĩnh viễn'
                        status = 'Bị cấm vĩnh viễn'
                    else:
                        durationText = f'{duration} ngày'
                        if lastBan['BAN_END']:
                            remaining = lastBan['BAN_END'
                                ] - Utils.getCurrentTime()
                            if remaining.total_seconds() > 0:
                                days = remaining.days
                                hours = remaining.seconds // 3600
                                status = f'Còn {days} ngày {hours} giờ'
                            else:
                                status = 'Đã hết hạn'
                        else:
                            status = 'Đang hoạt động'
                    formattedBanHistory = []
                    for ban in banHistory[-5:]:
                        formattedBanHistory.append({'banTime': ban[
                            'CREATED_AT'].strftime('%d/%m/%Y %H:%M'),
                            'reason': ban['BAN_REASON'], 'duration': 
                            f"{ban['BAN_DURATION']} ngày" if ban[
                            'BAN_DURATION'] != -1 else 'Vĩnh viễn'})
                    bannedUsers.append({'id': userId, 'username': user[
                        'USER_NAME'] or f'User{userId[:8]}', 'displayName':
                        user['DISPLAY_NAME'] or f'User{userId[:8]}',
                        'avatar': user['AVATAR_URL'], 'banCount': len(
                        banHistory), 'duration': durationText, 'reason':
                        lastBan['BAN_REASON'], 'status': status,
                        'banHistory': formattedBanHistory, 'allBanHistory':
                        banHistory})
            except Exception as e:
                print(f'Error processing banned user {userId}: {e}')
        stats = Utils.getStats()
        exchangeUsers = [u for u in users if u['totalPoints'] > 0]
        return render_template_string(self.getDashboardTemplate(), users=
            users, exchangeUsers=exchangeUsers, bannedUsers=bannedUsers,
            totalUsers=stats['totalUsers'], suspiciousCount=stats[
            'suspiciousCount'], bannedCount=stats['bannedCount'],
            totalPoints=stats['totalPoints'], totalTasks=stats['totalTasks'
            ], totalExchanges=stats['totalExchanges'])

    def getDashboardTemplate(self):
        try:
            with open('index.html', 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            return 'Dashboard template not found!'

    def run(self):
        logger.info('Flask server running on 0.0.0.0:2503')
        self.app.run(host='0.0.0.0', port=2503)


app = Flask(__name__)
flashServer = FlashServer()
app = flashServer.app
if __name__ == '__main__':
    server = FlashServer()
    server.run()
